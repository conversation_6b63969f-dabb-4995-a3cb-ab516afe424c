(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[461],{32922:function(e,s,r){Promise.resolve().then(r.bind(r,12011))},49804:function(e,s,r){"use strict";r.d(s,{Z:function(){return i}});var l=r(5853),o=r(97324),n=r(1153),t=r(2265),a=r(9496);let c=(0,n.fn)("Col"),i=t.forwardRef((e,s)=>{let{numColSpan:r=1,numColSpanSm:n,numColSpanMd:i,numColSpanLg:d,children:m,className:u}=e,g=(0,l._T)(e,["numColSpan","numColSpanSm","numColSpanMd","numColSpanLg","children","className"]),p=(e,s)=>e&&Object.keys(s).includes(String(e))?s[e]:"";return t.createElement("div",Object.assign({ref:s,className:(0,o.q)(c("root"),(()=>{let e=p(r,a.PT),s=p(n,a.SP),l=p(i,a.VS),t=p(d,a._w);return(0,o.q)(e,s,l,t)})(),u)},g),m)});i.displayName="Col"},67101:function(e,s,r){"use strict";r.d(s,{Z:function(){return d}});var l=r(5853),o=r(97324),n=r(1153),t=r(2265),a=r(9496);let c=(0,n.fn)("Grid"),i=(e,s)=>e&&Object.keys(s).includes(String(e))?s[e]:"",d=t.forwardRef((e,s)=>{let{numItems:r=1,numItemsSm:n,numItemsMd:d,numItemsLg:m,children:u,className:g}=e,p=(0,l._T)(e,["numItems","numItemsSm","numItemsMd","numItemsLg","children","className"]),f=i(r,a._m),h=i(n,a.LH),b=i(d,a.l5),w=i(m,a.N4),x=(0,o.q)(f,h,b,w);return t.createElement("div",Object.assign({ref:s,className:(0,o.q)(c("root"),"grid",x,g)},p),u)});d.displayName="Grid"},9496:function(e,s,r){"use strict";r.d(s,{LH:function(){return o},N4:function(){return t},PT:function(){return a},SP:function(){return c},VS:function(){return i},_m:function(){return l},_w:function(){return d},l5:function(){return n}});let l={0:"grid-cols-none",1:"grid-cols-1",2:"grid-cols-2",3:"grid-cols-3",4:"grid-cols-4",5:"grid-cols-5",6:"grid-cols-6",7:"grid-cols-7",8:"grid-cols-8",9:"grid-cols-9",10:"grid-cols-10",11:"grid-cols-11",12:"grid-cols-12"},o={0:"sm:grid-cols-none",1:"sm:grid-cols-1",2:"sm:grid-cols-2",3:"sm:grid-cols-3",4:"sm:grid-cols-4",5:"sm:grid-cols-5",6:"sm:grid-cols-6",7:"sm:grid-cols-7",8:"sm:grid-cols-8",9:"sm:grid-cols-9",10:"sm:grid-cols-10",11:"sm:grid-cols-11",12:"sm:grid-cols-12"},n={0:"md:grid-cols-none",1:"md:grid-cols-1",2:"md:grid-cols-2",3:"md:grid-cols-3",4:"md:grid-cols-4",5:"md:grid-cols-5",6:"md:grid-cols-6",7:"md:grid-cols-7",8:"md:grid-cols-8",9:"md:grid-cols-9",10:"md:grid-cols-10",11:"md:grid-cols-11",12:"md:grid-cols-12"},t={0:"lg:grid-cols-none",1:"lg:grid-cols-1",2:"lg:grid-cols-2",3:"lg:grid-cols-3",4:"lg:grid-cols-4",5:"lg:grid-cols-5",6:"lg:grid-cols-6",7:"lg:grid-cols-7",8:"lg:grid-cols-8",9:"lg:grid-cols-9",10:"lg:grid-cols-10",11:"lg:grid-cols-11",12:"lg:grid-cols-12"},a={1:"col-span-1",2:"col-span-2",3:"col-span-3",4:"col-span-4",5:"col-span-5",6:"col-span-6",7:"col-span-7",8:"col-span-8",9:"col-span-9",10:"col-span-10",11:"col-span-11",12:"col-span-12",13:"col-span-13"},c={1:"sm:col-span-1",2:"sm:col-span-2",3:"sm:col-span-3",4:"sm:col-span-4",5:"sm:col-span-5",6:"sm:col-span-6",7:"sm:col-span-7",8:"sm:col-span-8",9:"sm:col-span-9",10:"sm:col-span-10",11:"sm:col-span-11",12:"sm:col-span-12",13:"sm:col-span-13"},i={1:"md:col-span-1",2:"md:col-span-2",3:"md:col-span-3",4:"md:col-span-4",5:"md:col-span-5",6:"md:col-span-6",7:"md:col-span-7",8:"md:col-span-8",9:"md:col-span-9",10:"md:col-span-10",11:"md:col-span-11",12:"md:col-span-12",13:"md:col-span-13"},d={1:"lg:col-span-1",2:"lg:col-span-2",3:"lg:col-span-3",4:"lg:col-span-4",5:"lg:col-span-5",6:"lg:col-span-6",7:"lg:col-span-7",8:"lg:col-span-8",9:"lg:col-span-9",10:"lg:col-span-10",11:"lg:col-span-11",12:"lg:col-span-12",13:"lg:col-span-13"}},94789:function(e,s,r){"use strict";r.d(s,{Z:function(){return i}});var l=r(5853),o=r(2265),n=r(26898),t=r(97324),a=r(1153);let c=(0,a.fn)("Callout"),i=o.forwardRef((e,s)=>{let{title:r,icon:i,color:d,className:m,children:u}=e,g=(0,l._T)(e,["title","icon","color","className","children"]);return o.createElement("div",Object.assign({ref:s,className:(0,t.q)(c("root"),"flex flex-col overflow-hidden rounded-tremor-default text-tremor-default border-l-4 py-3 pr-3 pl-4",d?(0,t.q)((0,a.bM)(d,n.K.background).bgColor,(0,a.bM)(d,n.K.darkBorder).borderColor,(0,a.bM)(d,n.K.darkText).textColor,"dark:bg-opacity-10 bg-opacity-10"):(0,t.q)("bg-tremor-brand-faint border-tremor-brand-emphasis text-tremor-brand-emphasis","dark:bg-dark-tremor-brand-muted/70 dark:border-dark-tremor-brand-emphasis dark:text-dark-tremor-brand-emphasis"),m)},g),o.createElement("div",{className:(0,t.q)(c("header"),"flex items-start")},i?o.createElement(i,{className:(0,t.q)(c("icon"),"flex-none h-5 w-5 mr-1.5")}):null,o.createElement("h4",{className:(0,t.q)(c("title"),"font-semibold")},r)),o.createElement("p",{className:(0,t.q)(c("body"),"overflow-y-auto",u?"mt-2":"")},u))});i.displayName="Callout"},12011:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return k}});var l=r(57437),o=r(2265),n=r(99376),t=r(20831),a=r(94789),c=r(12514),i=r(49804),d=r(67101),m=r(84264),u=r(49566),g=r(96761),p=r(84566),f=r(19250),h=r(14474),b=r(13634),w=r(73002),x=r(3914);function k(){let[e]=b.Z.useForm(),s=(0,n.useSearchParams)();(0,x.e)("token");let r=s.get("invitation_id"),k=s.get("action"),[S,N]=(0,o.useState)(null),[y,_]=(0,o.useState)(""),[j,v]=(0,o.useState)(""),[C,Z]=(0,o.useState)(null),[E,I]=(0,o.useState)(""),[q,O]=(0,o.useState)(""),[P,T]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{(0,f.getUiConfig)().then(e=>{console.log("ui config in onboarding.tsx:",e),T(!1)})},[]),(0,o.useEffect)(()=>{r&&!P&&(0,f.getOnboardingCredentials)(r).then(e=>{let s=e.login_url;console.log("login_url:",s),I(s);let r=e.token,l=(0,h.o)(r);O(r),console.log("decoded:",l),N(l.key),console.log("decoded user email:",l.user_email),v(l.user_email),Z(l.user_id)})},[r,P]),(0,l.jsx)("div",{className:"mx-auto w-full max-w-md mt-10",children:(0,l.jsxs)(c.Z,{children:[(0,l.jsx)(g.Z,{className:"text-sm mb-5 text-center",children:"\uD83D\uDE85 LiteLLM"}),(0,l.jsx)(g.Z,{className:"text-xl",children:"reset_password"===k?"Reset Password":"Sign up"}),(0,l.jsx)(m.Z,{children:"reset_password"===k?"Reset your password to access Admin UI.":"Claim your user account to login to Admin UI."}),"reset_password"!==k&&(0,l.jsx)(a.Z,{className:"mt-4",title:"SSO",icon:p.GH$,color:"sky",children:(0,l.jsxs)(d.Z,{numItems:2,className:"flex justify-between items-center",children:[(0,l.jsx)(i.Z,{children:"SSO is under the Enterprise Tier."}),(0,l.jsx)(i.Z,{children:(0,l.jsx)(t.Z,{variant:"primary",className:"mb-2",children:(0,l.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:"Get Free Trial"})})})]})}),(0,l.jsxs)(b.Z,{className:"mt-10 mb-5 mx-auto",layout:"vertical",onFinish:e=>{console.log("in handle submit. accessToken:",S,"token:",q,"formValues:",e),S&&q&&(e.user_email=j,C&&r&&(0,f.claimOnboardingToken)(S,r,C,e.password).then(e=>{let s="/ui/";s+="?login=success",document.cookie="token="+q,console.log("redirecting to:",s);let r=(0,f.getProxyBaseUrl)();console.log("proxyBaseUrl:",r),r?window.location.href=r+s:window.location.href=s}))},children:[(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(b.Z.Item,{label:"Email Address",name:"user_email",children:(0,l.jsx)(u.Z,{type:"email",disabled:!0,value:j,defaultValue:j,className:"max-w-md"})}),(0,l.jsx)(b.Z.Item,{label:"Password",name:"password",rules:[{required:!0,message:"password required to sign up"}],help:"reset_password"===k?"Enter your new password":"Create a password for your account",children:(0,l.jsx)(u.Z,{placeholder:"",type:"password",className:"max-w-md"})})]}),(0,l.jsx)("div",{className:"mt-10",children:(0,l.jsx)(w.ZP,{htmlType:"submit",children:"reset_password"===k?"Reset Password":"Sign Up"})})]})]})})}},14474:function(e,s,r){"use strict";r.d(s,{o:function(){return o}});class l extends Error{}function o(e,s){let r;if("string"!=typeof e)throw new l("Invalid token specified: must be a string");s||(s={});let o=!0===s.header?0:1,n=e.split(".")[o];if("string"!=typeof n)throw new l(`Invalid token specified: missing part #${o+1}`);try{r=function(e){let s=e.replace(/-/g,"+").replace(/_/g,"/");switch(s.length%4){case 0:break;case 2:s+="==";break;case 3:s+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=s,decodeURIComponent(atob(r).replace(/(.)/g,(e,s)=>{let r=s.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(s)}}(n)}catch(e){throw new l(`Invalid token specified: invalid base64 for part #${o+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new l(`Invalid token specified: invalid json for part #${o+1} (${e.message})`)}}l.prototype.name="InvalidTokenError"}},function(e){e.O(0,[665,416,154,971,117,744],function(){return e(e.s=32922)}),_N_E=e.O()}]);